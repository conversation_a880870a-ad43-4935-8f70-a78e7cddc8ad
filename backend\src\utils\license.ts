import { eq } from 'drizzle-orm';
import { licenses } from '../../db/schema';
import { APIError, Errors } from '../../errors';

// 创建数据库实例的函数
let db: any;

export const initDb = (database: any) => {
  db = database;
};

export const validateLicense = async (licenseKey: string) => {
  if (!db) {
    throw new Error('数据库未初始化');
  }

  const license = await db.query.licenses.findFirst({
    where: eq(licenses.licenseKey, licenseKey),
    with: {
      version: {
        with: {
          product: true
        }
      }
    }
  });

  if (!license) {
    throw Errors.LICENSE_NOT_FOUND;
  }

  if (license.status !== 'ACTIVE') {
    throw Errors.LICENSE_INVALID;
  }

  return license;
};

export const safeJsonParse = (jsonString: string, fallback: any = {}) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('JSON解析错误:', error);
    return fallback;
  }
};