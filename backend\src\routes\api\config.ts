import { Hono } from 'hono';
import { eq } from 'drizzle-orm';
import { licenses } from '../../db/schema';
import { validateLicense, safeJsonParse } from '../utils/license';
import { cryptoService } from '../../utils/crypto';
import { 
  GetConfigResponse, 
  UpdateConfigRequest, 
  UpdateConfigResponse,
  VerifyConfig
} from '../../types/api';
import { successResponse, errorResponse } from '../../utils/response';
import { APIError, Errors } from '../../errors';

// 获取数据库实例
import { createDb } from '../../db';
let db: ReturnType<typeof createDb>;

const router = new Hono<{ Bindings: Env }>();

// 初始化数据库
router.use('*', async (c, next) => {
  if (!db) {
    db = createDb(c.env.DB);
  }
  await next();
});

// GET /api/config/{licenseKey} - 获取加密配置
router.get('/:licenseKey', async (c) => {
  const { licenseKey } = c.req.param();

  try {
    // 初始化工具函数的数据库实例
    const { initDb } = await import('../utils/license');
    initDb(db);

    // 1. 查询许可证及相关信息
    const license = await validateLicense(licenseKey);

    // 3. 解析配置模板和客户端配置
    const configTemplate = safeJsonParse(license.version.configTemplate);
    const verifyConfig = safeJsonParse(license.verifyConfig || '{}');

    // 4. 构建响应数据
    const response: GetConfigResponse = {
      configTemplate,
      verifyConfig,
      version: {
        id: license.version.id,
        version: license.version.version,
        versionName: license.version.versionName || ''
      },
      product: {
        id: license.version.product.id,
        name: license.version.product.name,
        description: license.version.product.description || ''
      }
    };

    // 5. 构建响应
    const responseData = successResponse(response);

    return c.json(responseData, 200);

  } catch (error) {
    if (error instanceof APIError) {
      return c.json(errorResponse(error.code, error.message, error.details), 400);
    }
    
    return c.json(errorResponse(5001, '内部服务器错误', error instanceof Error ? error.message : ''), 500);
  }
});

// PUT /api/config/{licenseKey} - 更新加密配置
router.put('/:licenseKey', async (c) => {
  const { licenseKey } = c.req.param();
  
  try {
    // 初始化工具函数的数据库实例
    const { initDb } = await import('../utils/license');
    initDb(db);

    // 1. 获取加密密钥和验证许可证
    const license = await validateLicense(licenseKey);

    // 2. 获取请求数据
    const body = await c.req.json();
    const { verifyConfig }: UpdateConfigRequest = body;

    // 3. 验证请求数据
    if (!verifyConfig || typeof verifyConfig !== 'object') {
      throw Errors.VALIDATION_ERROR;
    }

    // 4. 验证配置格式
    if (Object.keys(verifyConfig).length === 0) {
      throw new Error('配置不能为空');
    }

    // 5. 更新许可证配置
    await db.update(licenses)
      .set({
        verifyConfig: JSON.stringify(verifyConfig),
        updatedAt: new Date().toISOString()
      })
      .where(eq(licenses.id, license.id));

    // 6. 构建响应
    const response: UpdateConfigResponse = {
      updated: true,
      verifyConfig
    };

    const responseData = successResponse(response);

    return c.json(responseData, 200);

  } catch (error) {
    if (error instanceof APIError) {
      return c.json(errorResponse(error.code, error.message, error.details), 400);
    }
    
    return c.json(errorResponse(5001, '内部服务器错误', error instanceof Error ? error.message : ''), 500);
  }
});

export { router as configRouter };